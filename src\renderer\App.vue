<template>
  <div class="app">
    <!-- Main Content -->
    <main class="app-main">
      <!-- Auto-Scan Progress -->
      <div class="container">
        <AutoScanProgress
          :is-auto-scanning="isAutoScanning"
          :scan-status="scanStatus"
          :progress-percentage="progressPercentage"
          :processed-files="processedFiles"
          :total-files="totalFiles"
          :current-file="currentFile"
          :formatted-time-remaining="formattedTimeRemaining"
          :cache-hit-rate="cacheHitRate"
          :can-cancel="canCancel"
          :scan-results="scanResults"
          :scan-error="scanError"
          @cancel="cancelScan"
          @clear="clearResults"
          @retry="startAutoScan"
        />
      </div>

      <!-- Enhanced ModDashboard Interface -->
      <ModDashboard
        :mods="analysisResults"
        :is-loading="isAnalyzing"
        @analyze-folder="selectAndAnalyzeFolder"
        @export-results="exportResults"
        @open-settings="openSettings"
      />

      <!-- Error Display -->
      <div v-if="analysisError" class="container">
        <section class="error-section mb-xl">
          <div class="card">
            <div class="error-content">
              <div class="flex items-center gap-md mb-md">
                <ExclamationTriangleIcon class="w-6 h-6 text-error" />
                <h3 class="font-medium text-error">Analysis Error</h3>
              </div>
              <p class="text-muted mb-md">{{ analysisError }}</p>
              <button class="btn btn-secondary btn-sm" @click="clearError">
                Dismiss
              </button>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <div class="container">
        <div class="text-center py-md text-sm text-muted">
          <p>Simonitor - Built with ❤️ for the Sims 4 community</p>
        </div>
      </div>
    </footer>

    <!-- Settings Modal -->
    <Modal
      :is-open="isSettingsOpen"
      title="Settings"
      @close="closeSettings"
    >
      <AppSettings
        @settings-changed="handleSettingsChanged"
        ref="settingsRef"
      />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import {
  FolderOpenIcon,
  ArrowDownTrayIcon,
  CogIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

import ModDashboard from './components/ModDashboard.vue';
import FileUpload from './components/FileUpload.vue';
import Modal from './components/Modal.vue';
import AppSettings from './components/AppSettings.vue';
import AutoScanProgress from './components/AutoScanProgress.vue';

// Composables
import { useAppState } from './composables/useAppState';
import { useAutoScan } from './composables/useAutoScan';

// Types
import type { AnalyzedPackage, ResourceInfo } from '../types/analysis';

// Use composables for state management
const appState = useAppState();
const autoScan = useAutoScan();

// Destructure state and methods from composables
const {
  selectedFiles,
  analysisResults,
  isAnalyzing,
  analyzedCount,
  analysisError,
  isSettingsOpen,
  currentModsFolder,
  clearError,
  resetAnalysisState,
  handleFilesSelected,
  startAnalysis,
  finishAnalysis,
  setAnalysisError,
  processAnalysisResults,
  openSettings,
  closeSettings,
} = appState;

// Auto-scan state and methods
const {
  isAutoScanning,
  scanProgress,
  totalFiles,
  processedFiles,
  currentFile,
  cacheHitRate,
  canCancel,
  scanResults,
  scanError,
  scanStatus,
  progressPercentage,
  formattedTimeRemaining,
  startAutoScan,
  cancelScan,
  clearResults,
} = autoScan;

// Additional refs for UI components
const fileUploadRef = ref();
const settingsRef = ref();

// Event handlers - now using composable methods
async function handleAnalyzeRequested(files: File[]) {
  if (files.length === 0) return;

  if (!window.electronAPI) {
    setAnalysisError('Electron API not available. Please run in Electron app.');
    return;
  }

  startAnalysis();

  try {
    // Analyze files one by one
    for (const file of files) {
      const filePath = (file as any).path;
      
      // Create a promise to handle the async analysis
      const analysisPromise = new Promise<AnalyzedPackage>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Analysis timeout'));
        }, 30000); // 30 second timeout
        
        const handleResult = (result: any) => {
          clearTimeout(timeout);
          window.electronAPI.offAnalysisResult?.(handleResult);
          
          if (result.error) {
            reject(new Error(result.error));
          } else {
            resolve(result);
          }
        };
        
        window.electronAPI.onAnalysisResult(handleResult);
        window.electronAPI.analyzePackage(filePath);
      });
      
      try {
        const result = await analysisPromise;
        analysisResults.value.push(result);
        analyzedCount.value++;
      } catch (error) {
        analysisError.value = `Error analyzing ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        break;
      }
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'An unexpected error occurred';
  } finally {
    isAnalyzing.value = false;
  }
}

// Settings handlers - now using composable methods

function handleSettingsChanged(settings: any) {
  // Handle settings changes - could be used to update app behavior
}

// New enhanced methods for folder analysis
async function selectAndAnalyzeFolder() {
  try {
    if (!window.electronAPI) {
      setAnalysisError('Electron API not available. Please run in Electron app.');
      return;
    }

    startAnalysis();

    // Open folder selection dialog
    const folderResult = await window.electronAPI.selectModsFolder();

    if (!folderResult.success) {
      if (folderResult.error !== 'No folder selected') {
        setAnalysisError(folderResult.error);
      }
      finishAnalysis();
      return;
    }

    currentModsFolder.value = folderResult.path;

    // Analyze the entire folder
    const analysisResult = await window.electronAPI.analyzeModsFolder(folderResult.path);

    // Process analysis results using composable method
    await processAnalysisResults(analysisResult);
  } catch (error) {
    setAnalysisError(error instanceof Error ? error.message : 'Failed to analyze mods folder');
  } finally {
    finishAnalysis();
  }
}

async function exportResults() {
  if (analysisResults.value.length === 0) return;

  if (!window.electronAPI) {
    analysisError.value = 'Electron API not available. Please run in Electron app.';
    return;
  }

  try {
    // For now, export as JSON - could add format selection dialog
    const exportResult = await window.electronAPI.exportResults(analysisResults.value, 'json');

    if (exportResult.success) {
      // Could show a success notification here
    } else {
      analysisError.value = exportResult.error;
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'Failed to export results';
  }
}





// Auto-load default mods folder on startup
// Watch for auto-scan results and integrate with main analysis state
watch(scanResults, (newResults) => {
  if (newResults && newResults.length > 0) {
    console.log(`🔄 [App] Integrating ${newResults.length} auto-scan results`);
    processAnalysisResults(newResults);
  }
}, { immediate: true });

// Watch for auto-scan errors and integrate with main error state
watch(scanError, (newError) => {
  if (newError) {
    console.warn('🔄 [App] Auto-scan error:', newError);
    setAnalysisError(newError);
  }
});

onMounted(async () => {
  // Auto-scan will be handled by useAutoScan composable
  console.log('🚀 [App] Application mounted, auto-scan composable will handle startup scanning');
});

// Set up the analysis result handler (legacy support)
if (window.electronAPI?.onAnalysisResult) {
  window.electronAPI.onAnalysisResult((result: any) => {
    // This will be handled by the promise in handleAnalyzeRequested
  });
}
</script>

<style>
/* Import our enhanced design system */
@import './styles/simonitor-design-system.css';

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg,
    #1a1a1a 0%,
    color-mix(in srgb, #1a1a1a 85%, #BF3100) 30%,
    color-mix(in srgb, #1a1a1a 75%, #D76A03) 60%,
    color-mix(in srgb, #1a1a1a 70%, #EC9F05) 100%);
  font-family: var(--font-family-sans);
}

.app-header {
  background: linear-gradient(135deg,
    var(--bg-elevated) 0%,
    color-mix(in srgb, var(--primary-green) 8%, var(--bg-elevated)) 100%);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.app-title h1 {
  background: linear-gradient(135deg,
    var(--primary-green) 0%,
    var(--primary-orange) 50%,
    var(--primary-red-orange) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-bold);
}

.app-main {
  flex: 1;
}

.app-footer {
  background-color: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  margin-top: auto;
}

/* ===== MODERN BUTTON STYLES ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--duration-200) var(--ease-out);
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
  box-shadow: var(--button-shadow);
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--button-shadow-hover);
}

.btn-primary {
  background: var(--color-primary);
  color: var(--text-on-primary);
  border-color: var(--color-primary);
  box-shadow: 0 2px 4px color-mix(in srgb, var(--color-primary) 25%, transparent);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  box-shadow: 0 4px 12px color-mix(in srgb, var(--color-primary) 30%, transparent);
}

.btn-primary:active:not(:disabled) {
  background: var(--color-primary-active);
  border-color: var(--color-primary-active);
}

.btn-secondary {
  background: var(--color-secondary-light);
  color: var(--color-secondary);
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-secondary);
  color: var(--text-on-secondary);
  border-color: var(--color-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px color-mix(in srgb, var(--color-secondary) 25%, transparent);
}

.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
}

.badge-info {
  background: var(--info-bg);
  color: var(--info);
  border: 1px solid var(--info-border);
}

/* Card styles */
.card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

/* Container */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Component-specific styles */

.welcome-content {
  max-width: 600px;
  margin: 0 auto;
}

.welcome-icon {
  color: var(--text-muted);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.feature-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.feature-item h4 {
  margin: 0 0 var(--spacing-sm) 0;
}

.feature-item p {
  margin: 0;
}

.max-w-md {
  max-width: 28rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.text-error {
  color: var(--error-color);
}

.loading-content .loading {
  border-width: 3px;
}

@media (max-width: 768px) {
  .app-title h1 {
    font-size: 1.5rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .feature-item {
    padding: var(--spacing-md);
  }
}
</style>
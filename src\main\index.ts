import { app, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron';
import * as path from 'path';
import * as fs from 'fs/promises';
import { sanitizePath } from '../utils/path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

/**
 * Creates a serializable version of analysis results by removing non-serializable objects
 */
function createSerializableResult(result: any): any {
    if (!result) return result;

    // Create a deep copy and remove problematic properties
    const serializable = JSON.parse(JSON.stringify(result, (key, value) => {
        // Skip functions, symbols, and other non-serializable types
        if (typeof value === 'function' || typeof value === 'symbol') {
            return undefined;
        }

        // Skip Buffer objects and replace with size info
        if (value && value.type === 'Buffer' && Array.isArray(value.data)) {
            return { type: 'Buffer', size: value.data.length };
        }

        // Skip complex S4TK objects but keep basic properties
        if (value && typeof value === 'object' && value.constructor &&
            value.constructor.name && value.constructor.name.includes('Resource')) {
            return { type: value.constructor.name, size: value.size || 0 };
        }

        return value;
    }));

    // Ensure critical fields for UI are preserved
    if (result.filePath) {
        serializable.filePath = result.filePath;
    }
    if (result.fileName) {
        serializable.fileName = result.fileName;
    }

    return serializable;
}

function createWindow() {
    const mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        webPreferences: {
            preload: path.join(__dirname, '../preload/index.js'),
            nodeIntegration: false,
            contextIsolation: true,
            webSecurity: false, // Disable web security for development
            allowRunningInsecureContent: true,
            experimentalFeatures: true,
        },
        titleBarStyle: 'hiddenInset',
        show: false, // Don't show until ready
    });

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // electron-vite will handle this automatically
    if (process.env.VITE_DEV_SERVER_URL) {
        mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL);
        // Open DevTools in development
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    } else {
        mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    return mainWindow;
}

app.whenReady().then(() => {
    const analysisService = new PackageAnalysisService();
    let mainWindow: BrowserWindow;

    // IPC Handlers

    // Single file analysis
    ipcMain.handle('analyze-package', async (event, filePath: string) => {
        try {
            const modsFolder = app.getPath('documents'); // A safe-ish base folder
            const sanitizedPath = sanitizePath(modsFolder, filePath);
            console.log(`Analyzing file: ${sanitizedPath}`);
            const buffer = await fs.readFile(sanitizedPath);
            const result = await analysisService.detailedAnalyzeAsync(buffer, sanitizedPath);

            // Create a serializable version of the result
            const serializableResult = createSerializableResult(result);

            return { success: true, data: serializableResult };
        } catch (error) {
            console.error('Analysis error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'An unknown error occurred.'
            };
        }
    });

    // Batch analysis for entire mod folder
    ipcMain.handle('analyze-mods-folder', async (event, folderPath: string) => {
        try {
            const modsFolder = app.getPath('documents'); // A safe-ish base folder
            const sanitizedPath = sanitizePath(modsFolder, folderPath);
            console.log(`Analyzing mods folder: ${sanitizedPath}`);
            const results = await analyzeModsFolder(sanitizedPath, analysisService);

            // Create serializable versions of all results
            const serializableResults = results.map(result => createSerializableResult(result));

            return { success: true, data: serializableResults };
        } catch (error) {
            console.error('Folder analysis error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to analyze mods folder.'
            };
        }
    });

    // Background analysis with progress reporting
    ipcMain.handle('analyze-mods-folder-background', async (event, folderPath: string, options: any = {}) => {
        try {
            const modsFolder = app.getPath('documents');
            const sanitizedPath = sanitizePath(modsFolder, folderPath);
            console.log(`🚀 [Background] Starting background analysis of: ${sanitizedPath}`);

            const results = await analyzeModsFolderWithProgress(
                sanitizedPath,
                analysisService,
                (progress) => {
                    // Send progress updates to renderer
                    mainWindow.webContents.send('scan-progress', progress);
                },
                options
            );

            // Create serializable versions of all results
            const serializableResults = results.map(result => createSerializableResult(result));

            return { success: true, data: serializableResults };
        } catch (error) {
            console.error('Background analysis error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to analyze mods folder in background.'
            };
        }
    });

    // Get default Sims 4 mods folder
    ipcMain.handle('get-default-mods-folder', async () => {
        try {
            const userHome = require('os').homedir();
            const defaultPath = path.join(userHome, 'Documents', 'Electronic Arts', 'The Sims 4', 'Mods');

            // Check if the folder exists
            const exists = await fs.access(defaultPath).then(() => true).catch(() => false);

            return {
                success: true,
                path: defaultPath,
                exists: exists
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to get default mods folder'
            };
        }
    });

    // Open folder dialog
    ipcMain.handle('select-mods-folder', async () => {
        try {
            const result = await dialog.showOpenDialog(mainWindow, {
                properties: ['openDirectory'],
                title: 'Select Sims 4 Mods Folder',
                defaultPath: path.join(require('os').homedir(), 'Documents', 'Electronic Arts', 'The Sims 4', 'Mods')
            });

            if (!result.canceled && result.filePaths.length > 0) {
                return { success: true, path: result.filePaths[0] };
            } else {
                return { success: false, error: 'No folder selected' };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to open folder dialog'
            };
        }
    });

    // Export analysis results
    ipcMain.handle('export-results', async (event, data: any, format: 'json' | 'csv') => {
        try {
            const result = await dialog.showSaveDialog(mainWindow, {
                title: 'Export Analysis Results',
                defaultPath: `simonitor-analysis-${new Date().toISOString().split('T')[0]}.${format}`,
                filters: [
                    { name: format.toUpperCase(), extensions: [format] }
                ]
            });

            if (!result.canceled && result.filePath) {
                let content: string;
                if (format === 'json') {
                    content = JSON.stringify(data, null, 2);
                } else {
                    content = convertToCSV(data);
                }

                await fs.writeFile(result.filePath, content, 'utf8');
                return { success: true, path: result.filePath };
            } else {
                return { success: false, error: 'Export cancelled' };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to export results'
            };
        }
    });

    // Thumbnail extraction
    ipcMain.handle('extract-thumbnails', async (event, filePath: string, options: any = {}) => {
        try {
            const modsFolder = app.getPath('documents'); // A safe-ish base folder
            const sanitizedPath = sanitizePath(modsFolder, filePath);
            console.log(`Extracting thumbnails from: ${sanitizedPath}`);

            // Import the thumbnail extraction service
            const { ThumbnailExtractionService } = await import('../services/visual/ThumbnailExtractionService');

            // Read the file
            const buffer = await fs.readFile(sanitizedPath);

            // Extract thumbnails with enhanced settings for multiple variations
            const result = await ThumbnailExtractionService.extractThumbnails(
                buffer,
                path.basename(sanitizedPath),
                {
                    maxThumbnails: 25, // Allow up to 25 thumbnails for color variations
                    preferredFormat: 'webp',
                    maxWidth: 256,
                    maxHeight: 256,
                    prioritizeCasThumbnails: true, // 🎯 Enable smart primary thumbnail selection
                    generateFallbacks: true,
                    ...options
                }
            );

            return {
                success: result.success,
                thumbnails: result.thumbnails,
                errors: result.errors,
                processingTime: result.processingTime
            };
        } catch (error) {
            console.error('Thumbnail extraction failed:', error);
            return {
                success: false,
                thumbnails: [],
                errors: [error instanceof Error ? error.message : 'Unknown thumbnail extraction error'],
                processingTime: 0
            };
        }
    });

    mainWindow = createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            mainWindow = createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Helper Functions

/**
 * Recursively analyzes all mod files in a folder with progress reporting
 */
function analyzeModsFolderWithProgress(
    folderPath: string,
    analysisService: PackageAnalysisService,
    onProgress?: (progress: any) => void,
    options: any = {}
): Promise<any[]> {
    const results: any[] = [];
    let totalFiles = 0;
    let processedFiles = 0;
    const startTime = Date.now();

    // First pass: count total files
    async function countFiles(dirPath: string): Promise<number> {
        let count = 0;
        try {
            const items = await fs.readdir(dirPath, { withFileTypes: true });
            for (const item of items) {
                const fullPath = path.join(dirPath, item.name);
                if (item.isDirectory()) {
                    count += await countFiles(fullPath);
                } else if (item.isFile()) {
                    const ext = path.extname(item.name).toLowerCase();
                    if (ext === '.package' || ext === '.ts4script') {
                        count++;
                    }
                }
            }
        } catch (error) {
            console.warn(`Failed to count files in ${dirPath}:`, error);
        }
        return count;
    }

    // Second pass: analyze files with progress
    async function scanDirectory(dirPath: string): Promise<void> {
        try {
            const items = await fs.readdir(dirPath, { withFileTypes: true });

            for (const item of items) {
                const fullPath = path.join(dirPath, item.name);

                if (item.isDirectory()) {
                    await scanDirectory(fullPath);
                } else if (item.isFile()) {
                    const ext = path.extname(item.name).toLowerCase();
                    if (ext === '.package' || ext === '.ts4script') {
                        try {
                            console.log(`🔍 [Background] Analyzing: ${fullPath}`);

                            // Report progress
                            onProgress?.({
                                type: 'file-start',
                                currentFile: item.name,
                                processedFiles,
                                totalFiles,
                                progress: totalFiles > 0 ? (processedFiles / totalFiles) * 100 : 0,
                                estimatedTimeRemaining: calculateTimeRemaining(processedFiles, totalFiles, startTime)
                            });

                            // Get file stats for caching
                            const stats = await fs.stat(fullPath);
                            const fileStats = {
                                lastModified: stats.mtime.getTime(),
                                size: stats.size
                            };

                            // Check cache first if enabled
                            let result: any = null;
                            let cacheHit = false;

                            if (options.enableCaching) {
                                // Note: Cache checking would be implemented in renderer process
                                // For now, we'll proceed with analysis
                            }

                            // Read file buffer (needed for both analysis and thumbnails)
                            const buffer = await fs.readFile(fullPath);

                            if (!result) {
                                result = await analysisService.detailedAnalyzeAsync(buffer, fullPath);
                            } else {
                                cacheHit = true;
                                console.log(`💾 [Background] Cache hit for: ${fullPath}`);
                            }

                            // Add file metadata
                            result.fileMetadata = {
                                size: stats.size,
                                lastModified: stats.mtime,
                                createdAt: stats.birthtime,
                                cacheHit
                            };

                            result.filePath = fullPath;
                            (result as any).fileName = item.name;

                            // Extract thumbnails
                            try {
                                const { ThumbnailExtractionService } = await import('../services/visual/ThumbnailExtractionService');
                                const thumbnailResult = await ThumbnailExtractionService.extractThumbnails(
                                    buffer,
                                    item.name,
                                    {
                                        maxThumbnails: 25,
                                        preferredFormat: 'webp',
                                        maxWidth: 256,
                                        maxHeight: 256,
                                        prioritizeCasThumbnails: true,
                                        generateFallbacks: true,
                                        quality: 85
                                    }
                                );

                                if (thumbnailResult.success && thumbnailResult.thumbnails.length > 0) {
                                    result.thumbnails = thumbnailResult.thumbnails;
                                    result.primaryThumbnail = thumbnailResult.thumbnails[0];
                                }
                            } catch (thumbnailError) {
                                console.warn(`Thumbnail extraction failed for ${item.name}:`, thumbnailError);
                            }

                            results.push(result);
                            processedFiles++;

                            // Report completion
                            onProgress?.({
                                type: 'file-complete',
                                currentFile: item.name,
                                processedFiles,
                                totalFiles,
                                progress: totalFiles > 0 ? (processedFiles / totalFiles) * 100 : 0,
                                estimatedTimeRemaining: calculateTimeRemaining(processedFiles, totalFiles, startTime)
                            });

                        } catch (error) {
                            console.error(`Failed to analyze ${fullPath}:`, error);
                            processedFiles++;

                            onProgress?.({
                                type: 'file-error',
                                currentFile: item.name,
                                error: error instanceof Error ? error.message : 'Unknown error',
                                processedFiles,
                                totalFiles,
                                progress: totalFiles > 0 ? (processedFiles / totalFiles) * 100 : 0,
                                estimatedTimeRemaining: calculateTimeRemaining(processedFiles, totalFiles, startTime)
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`Failed to scan directory ${dirPath}:`, error);
        }
    }

    return (async () => {
        try {
            // Count total files first
            console.log('📊 [Background] Counting files...');
            totalFiles = await countFiles(folderPath);

            onProgress?.({
                type: 'scan-start',
                totalFiles,
                processedFiles: 0,
                progress: 0
            });

            // Then analyze with progress
            await scanDirectory(folderPath);

            onProgress?.({
                type: 'scan-complete',
                totalFiles,
                processedFiles,
                progress: 100,
                results: results.length,
                duration: Date.now() - startTime
            });

            return results;
        } catch (error) {
            onProgress?.({
                type: 'scan-error',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw error;
        }
    })();
}

function calculateTimeRemaining(processed: number, total: number, startTime: number): number {
    if (processed === 0) return 0;
    const elapsed = (Date.now() - startTime) / 1000;
    const rate = processed / elapsed;
    const remaining = total - processed;
    return remaining / rate;
}

/**
 * Recursively analyzes all mod files in a folder (original function)
 */
function analyzeModsFolder(folderPath: string, analysisService: PackageAnalysisService): Promise<any[]> {
    const results: any[] = [];

    async function scanDirectory(dirPath: string): Promise<void> {
        try {
            const items = await fs.readdir(dirPath, { withFileTypes: true });

            for (const item of items) {
                const fullPath = path.join(dirPath, item.name);

                if (item.isDirectory()) {
                    // Recursively scan subdirectories
                    await scanDirectory(fullPath);
                } else if (item.isFile()) {
                    const ext = path.extname(item.name).toLowerCase();
                    if (ext === '.package' || ext === '.ts4script') {
                        try {
                            console.log(`Analyzing: ${fullPath}`);
                            const buffer = await fs.readFile(fullPath);
                            const result = await analysisService.detailedAnalyzeAsync(buffer, fullPath);

                            // Add file metadata and ensure filePath is preserved
                            const stats = await fs.stat(fullPath);
                            result.fileMetadata = {
                                size: stats.size,
                                lastModified: stats.mtime,
                                createdAt: stats.birthtime
                            };

                            // Ensure filePath is explicitly set for UI thumbnail extraction
                            result.filePath = fullPath;
                            (result as any).fileName = item.name;

                            // 🚀 AUTOMATIC THUMBNAIL EXTRACTION
                            // Extract thumbnails immediately during analysis for seamless UX
                            try {
                                console.log(`🎨 [AutoThumbnails] Extracting thumbnails for: ${item.name}`);

                                // Dynamic import to avoid circular dependencies
                                const { ThumbnailExtractionService } = await import('../services/visual/ThumbnailExtractionService');

                                const thumbnailResult = await ThumbnailExtractionService.extractThumbnails(
                                    buffer,
                                    item.name,
                                    {
                                        maxThumbnails: 25, // Allow multiple color variations
                                        preferredFormat: 'webp',
                                        maxWidth: 256,
                                        maxHeight: 256,
                                        prioritizeCasThumbnails: true, // 🎯 Enable smart primary thumbnail selection
                                        generateFallbacks: true,
                                        quality: 85
                                    }
                                );

                                if (thumbnailResult.success && thumbnailResult.thumbnails.length > 0) {
                                    // Attach thumbnail data as additional properties (not part of strict type)
                                    (result as any).thumbnails = thumbnailResult.thumbnails;
                                    (result as any).primaryThumbnail = thumbnailResult.thumbnails[0];
                                    (result as any).thumbnailVariations = thumbnailResult.thumbnails.slice(1);
                                    (result as any).hasMultipleVariations = thumbnailResult.thumbnails.length > 1;
                                    (result as any).thumbnailUrl = thumbnailResult.thumbnails[0]?.imageData;

                                    console.log(`✅ [AutoThumbnails] Extracted ${thumbnailResult.thumbnails.length} thumbnails for: ${item.name}`);
                                } else {
                                    console.log(`ℹ️ [AutoThumbnails] No thumbnails found for: ${item.name}`);
                                }
                            } catch (thumbnailError) {
                                console.warn(`⚠️ [AutoThumbnails] Failed to extract thumbnails for ${item.name}:`, thumbnailError);
                                // Continue with analysis even if thumbnail extraction fails
                            }

                            results.push(result);
                        } catch (error) {
                            console.error(`Error analyzing ${fullPath}:`, error);
                            // Add error entry
                            results.push({
                                fileName: item.name,
                                filePath: fullPath,
                                error: error instanceof Error ? error.message : 'Analysis failed',
                                hasError: true
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`Error scanning directory ${dirPath}:`, error);
        }
    }

    return scanDirectory(folderPath).then(() => results);
}

/**
 * Converts analysis results to CSV format
 */
function convertToCSV(data: any[]): string {
    if (data.length === 0) return '';

    // Define CSV headers (Enhanced with CAS data)
    const headers = [
        'File Name',
        'File Path',
        'File Size',
        'File Type',
        'Author',
        'Version',
        'Resource Count',
        'Has Intelligence',
        'Intelligence Type',
        'Quality Score',
        'Risk Level',
        'Category',
        'Content Type',
        'Performance Impact',
        'Custom Content',
        'Processing Time',
        'Error',
        // Enhanced CAS Hair Classification Headers
        'CAS Category',
        'CAS Subcategory',
        'Hair Length',
        'Hair Style',
        'Hair Texture',
        'Hair Has Accessories',
        'Hair Confidence',
        'Hair Detection Method'
    ];

    // Convert data to CSV rows (Enhanced with CAS data)
    const rows = data.map(item => {
        // Extract enhanced CAS hair data
        const casItem = item.casContent?.items?.[0];
        const hairDetails = casItem?.hairDetails;

        return [
            item.fileName || '',
            item.filePath || '',
            item.fileSize || 0,
            item.fileExtension || '',
            item.author || '',
            item.version || '',
            item.resourceCount || 0,
            item.hasResourceIntelligence ? 'Yes' : 'No',
            item.intelligenceType || '',
            item.qualityScore || '',
            item.riskLevel || '',
            item.resourceIntelligenceData?.category || '',
            item.resourceIntelligenceData?.contentType || '',
            item.resourceIntelligenceData?.performance?.estimatedImpact || '',
            item.resourceIntelligenceData?.customContent?.isCustomContent ? 'Yes' : 'No',
            item.processingTime || 0,
            item.error || '',
            // Enhanced CAS Hair Classification Data
            casItem?.category || '',
            casItem?.subcategory || '',
            hairDetails?.length || '',
            hairDetails?.style?.join(', ') || '',
            hairDetails?.texture || '',
            hairDetails?.hasAccessories ? 'Yes' : 'No',
            hairDetails?.confidence ? `${Math.round(hairDetails.confidence * 100)}%` : '',
            hairDetails?.detectionMethod || ''
        ];
    });

    // Escape CSV values
    const escapeCSV = (value: any): string => {
        const str = String(value);
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
            return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
    };

    // Build CSV content
    const csvContent = [
        headers.map(escapeCSV).join(','),
        ...rows.map(row => row.map(escapeCSV).join(','))
    ].join('\n');

    return csvContent;
}
<template>
  <div class="app-settings">
    <div class="settings-header mb-lg">
      <h3 class="font-semibold text-lg">Settings</h3>
      <p class="text-muted text-sm">Customize your Simonitor experience</p>
    </div>

    <div class="settings-content">
      <!-- Auto-Scan Settings -->
      <div class="settings-section mb-lg">
        <h4 class="font-medium mb-md">Auto-Scan Options</h4>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.enableAutoScan"
              class="form-checkbox"
            />
            Enable automatic scanning on startup
          </label>
          <p class="text-sm text-muted mt-xs">
            Automatically scan your mods folder when Simon<PERSON> starts
          </p>
        </div>

        <div class="form-group" v-if="settings.enableAutoScan">
          <label class="form-label">Default mods folder</label>
          <div class="flex gap-2">
            <input
              type="text"
              v-model="settings.defaultModsFolder"
              class="form-input flex-1"
              placeholder="C:\Users\<USER>\Documents\Electronic Arts\The Sims 4\Mods"
              readonly
            />
            <button
              type="button"
              @click="selectDefaultModsFolder"
              class="btn btn-secondary"
            >
              Browse
            </button>
          </div>
          <p class="text-sm text-muted mt-xs">
            The folder to scan automatically on startup
          </p>
        </div>

        <div class="form-group" v-if="settings.enableAutoScan">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.enableScanCaching"
              class="form-checkbox"
            />
            Enable scan result caching
          </label>
          <p class="text-sm text-muted mt-xs">
            Cache scan results to avoid re-scanning unchanged files (improves startup speed)
          </p>
        </div>

        <div class="form-group" v-if="settings.enableAutoScan">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.allowBackgroundScanning"
              class="form-checkbox"
            />
            Allow background scanning
          </label>
          <p class="text-sm text-muted mt-xs">
            Continue scanning in the background while you use other features
          </p>
        </div>
      </div>

      <!-- Analysis Settings -->
      <div class="settings-section mb-lg">
        <h4 class="font-medium mb-md">Analysis Options</h4>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.autoExpandFirstFile"
              class="form-checkbox"
            />
            Auto-expand first analyzed file
          </label>
          <p class="text-sm text-muted mt-xs">
            Automatically expand the first file's resources when analysis completes
          </p>
        </div>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.showResourceSizes"
              class="form-checkbox"
            />
            Show resource sizes in results
          </label>
          <p class="text-sm text-muted mt-xs">
            Display compressed and decompressed sizes for resources
          </p>
        </div>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.enableOverrideDetection"
              class="form-checkbox"
            />
            Enable override detection (Coming Soon)
          </label>
          <p class="text-sm text-muted mt-xs">
            Detect when mods override base game resources
          </p>
        </div>
      </div>

      <!-- Enhanced CAS Classification Settings -->
      <div class="settings-section mb-lg">
        <h4 class="font-medium mb-md">Enhanced CAS Classification</h4>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.enableEnhancedCASDetection"
              class="form-checkbox"
            />
            Enable enhanced CAS detection
          </label>
          <p class="text-sm text-muted mt-xs">
            Use filename analysis to improve CAS content categorization
          </p>
        </div>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.showHairClassificationDetails"
              class="form-checkbox"
            />
            Show detailed hair classification
          </label>
          <p class="text-sm text-muted mt-xs">
            Display hair length, style, texture, and confidence information
          </p>
        </div>

        <div class="form-group">
          <label class="form-label">Hair classification confidence threshold</label>
          <select v-model="settings.hairConfidenceThreshold" class="form-input">
            <option value="0.3">Low (30%)</option>
            <option value="0.5">Medium (50%)</option>
            <option value="0.7">High (70%)</option>
            <option value="0.9">Very High (90%)</option>
          </select>
          <p class="text-sm text-muted mt-xs">
            Minimum confidence required to display hair classification results
          </p>
        </div>
      </div>

      <!-- Display Settings -->
      <div class="settings-section mb-lg">
        <h4 class="font-medium mb-md">Display Options</h4>
        
        <div class="form-group">
          <label class="form-label">Results per page</label>
          <select v-model="settings.resultsPerPage" class="form-input">
            <option value="25">25 resources</option>
            <option value="50">50 resources</option>
            <option value="100">100 resources</option>
            <option value="all">Show all</option>
          </select>
          <p class="text-sm text-muted mt-xs">
            Number of resources to display per file
          </p>
        </div>

        <div class="form-group">
          <label class="form-label">Default sort order</label>
          <select v-model="settings.defaultSortField" class="form-input">
            <option value="key">Resource Key</option>
            <option value="type">Resource Type</option>
            <option value="group">Group ID</option>
            <option value="instance">Instance ID</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.useMonospaceFont"
              class="form-checkbox"
            />
            Use monospace font for resource data
          </label>
          <p class="text-sm text-muted mt-xs">
            Better alignment for resource keys and IDs
          </p>
        </div>
      </div>

      <!-- Export Settings -->
      <div class="settings-section mb-lg">
        <h4 class="font-medium mb-md">Export Options</h4>
        
        <div class="form-group">
          <label class="form-label">Default export format</label>
          <select v-model="settings.defaultExportFormat" class="form-input">
            <option value="json">JSON</option>
            <option value="csv">CSV</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.includeFilePathInExport"
              class="form-checkbox"
            />
            Include full file paths in exports
          </label>
          <p class="text-sm text-muted mt-xs">
            Export complete file paths instead of just filenames
          </p>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div class="settings-section mb-lg">
        <h4 class="font-medium mb-md">Advanced</h4>
        
        <div class="form-group">
          <label class="form-label">Analysis timeout (seconds)</label>
          <input
            type="number"
            v-model.number="settings.analysisTimeout"
            min="10"
            max="300"
            class="form-input"
          />
          <p class="text-sm text-muted mt-xs">
            Maximum time to wait for file analysis (10-300 seconds)
          </p>
        </div>

        <div class="form-group">
          <label class="form-label">
            <input
              type="checkbox"
              v-model="settings.enableDebugMode"
              class="form-checkbox"
            />
            Enable debug mode
          </label>
          <p class="text-sm text-muted mt-xs">
            Show additional debugging information in console
          </p>
        </div>
      </div>

      <!-- Actions -->
      <div class="settings-actions">
        <button class="btn btn-primary" @click="saveSettings">
          Save Settings
        </button>
        <button class="btn btn-secondary" @click="resetSettings">
          Reset to Defaults
        </button>
        <button class="btn btn-secondary" @click="exportSettings">
          Export Settings
        </button>
        <button class="btn btn-secondary" @click="importSettings">
          Import Settings
        </button>
      </div>
    </div>

    <!-- Hidden file input for settings import -->
    <input
      ref="settingsFileInput"
      type="file"
      accept=".json"
      @change="handleSettingsImport"
      style="display: none;"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits } from 'vue';

// Settings interface
interface AppSettings {
  // Auto-Scan Settings
  enableAutoScan: boolean;
  defaultModsFolder: string;
  enableScanCaching: boolean;
  allowBackgroundScanning: boolean;
  // Analysis Settings
  autoExpandFirstFile: boolean;
  showResourceSizes: boolean;
  enableOverrideDetection: boolean;
  resultsPerPage: string;
  defaultSortField: string;
  useMonospaceFont: boolean;
  defaultExportFormat: string;
  includeFilePathInExport: boolean;
  analysisTimeout: number;
  enableDebugMode: boolean;
  // Enhanced CAS Classification Settings
  enableEnhancedCASDetection: boolean;
  showHairClassificationDetails: boolean;
  hairConfidenceThreshold: number;
}

// Default settings
const defaultSettings: AppSettings = {
  // Auto-Scan Defaults
  enableAutoScan: true,
  defaultModsFolder: '',
  enableScanCaching: true,
  allowBackgroundScanning: true,
  // Analysis Defaults
  autoExpandFirstFile: true,
  showResourceSizes: true,
  enableOverrideDetection: false,
  resultsPerPage: '50',
  defaultSortField: 'key',
  useMonospaceFont: true,
  defaultExportFormat: 'json',
  includeFilePathInExport: true,
  analysisTimeout: 30,
  enableDebugMode: false,
  // Enhanced CAS Classification Defaults
  enableEnhancedCASDetection: true,
  showHairClassificationDetails: true,
  hairConfidenceThreshold: 0.5,
};

// Emits
const emit = defineEmits<{
  settingsChanged: [settings: AppSettings];
}>();

// Reactive state
const settings = reactive<AppSettings>({ ...defaultSettings });
const settingsFileInput = ref<HTMLInputElement>();

// Methods
function saveSettings() {
  try {
    localStorage.setItem('simonitor-settings', JSON.stringify(settings));
    emit('settingsChanged', { ...settings });
    
    // Show success feedback (you could add a toast notification here)
    console.log('Settings saved successfully');
  } catch (error) {
    console.error('Failed to save settings:', error);
  }
}

function loadSettings() {
  try {
    const saved = localStorage.getItem('simonitor-settings');
    if (saved) {
      const parsedSettings = JSON.parse(saved);
      Object.assign(settings, { ...defaultSettings, ...parsedSettings });
      emit('settingsChanged', { ...settings });
    }
  } catch (error) {
    console.error('Failed to load settings:', error);
    // Reset to defaults if loading fails
    Object.assign(settings, defaultSettings);
  }
}

function resetSettings() {
  Object.assign(settings, defaultSettings);
  saveSettings();
}

function exportSettings() {
  try {
    const settingsJson = JSON.stringify(settings, null, 2);
    const blob = new Blob([settingsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'simonitor-settings.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Failed to export settings:', error);
  }
}

function importSettings() {
  settingsFileInput.value?.click();
}

function handleSettingsImport(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  
  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string);
        Object.assign(settings, { ...defaultSettings, ...importedSettings });
        saveSettings();
        console.log('Settings imported successfully');
      } catch (error) {
        console.error('Failed to import settings:', error);
      }
    };
    reader.readAsText(file);
  }
  
  // Reset the input
  if (target) {
    target.value = '';
  }
}

// Load settings on component mount
onMounted(() => {
  loadSettings();
});

// Expose settings for parent component
defineExpose({
  settings,
  saveSettings,
  loadSettings,
  resetSettings
});
</script>

<style scoped>
/* Import design system */
@import '../styles/simonitor-design-system.css';

.settings-section {
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.settings-section:last-of-type {
  border-bottom: none;
}

.form-checkbox {
  margin-right: var(--space-2);
  accent-color: var(--primary);
}

.form-label {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  cursor: pointer;
}

.form-label input[type="checkbox"] {
  margin-top: 2px;
  flex-shrink: 0;
}

.settings-actions {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-light);
}

@media (max-width: 768px) {
  .settings-actions {
    flex-direction: column;
  }
  
  .settings-actions .btn {
    width: 100%;
  }
}
</style>
/**
 * Thumbnail Extraction Service
 * 
 * Extracts thumbnails from Sims 4 mod files for visual category browsing.
 * Supports multiple image formats and provides web-compatible thumbnail generation.
 * 
 * Addresses Reddit request: "visual categories with thumbnails and sidebar navigation"
 */

import { Package } from '@s4tk/models';
import { DdsImageResource } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import type { ResourceEntry } from '@s4tk/models/types';
import { PackageParserService } from '../shared/PackageParserService';
import { RESOURCE_GROUPS } from '../../constants/ResourceTypeRegistry';
import { ThumbnailCacheService } from '../cache/ThumbnailCacheService';

// Import S4TK Images for advanced DDS/DST/RLE2/RLES processing
import { DdsImage } from '@s4tk/images';

// Import zlib for RLE2 decompression
import * as zlib from 'zlib';

// Import DDS processing libraries (with browser compatibility check)
let decodeDXT: any = null;
let parseDDS: any = null;

// Check if we're in a Node.js environment (Electron main process)
const isNodeEnvironment = typeof window === 'undefined' && typeof require !== 'undefined';

if (isNodeEnvironment) {
    try {
        decodeDXT = require('decode-dxt');
        parseDDS = require('parse-dds');
    } catch (error) {
        console.warn('DDS processing libraries not available:', error);
    }
}

export interface ThumbnailData {
    id: string;
    modFileName: string;
    resourceType: string;
    resourceKey: string;
    imageData: string; // Base64 encoded image data or data URL
    format: 'png' | 'jpg' | 'webp' | 'svg';
    width: number;
    height: number;
    category: string;
    subcategory?: string;
    confidence: number;
    extractionMethod: 'cas_thumbnail' | 'dds_conversion' | 'dst_conversion' | 'rle2_conversion' | 'rles_conversion' | 'png_direct' | 'fallback_icon';
    fileSize: number;
    isHighQuality: boolean;
    isFallback?: boolean;
}

export interface ThumbnailExtractionResult {
    success: boolean;
    thumbnails: ThumbnailData[];
    errors: string[];
    processingTime: number;
    totalImagesFound: number;
    thumbnailsExtracted: number;
}

export interface ThumbnailExtractionOptions {
    maxThumbnails?: number;
    preferredFormat?: 'png' | 'jpg' | 'webp';
    maxWidth?: number;
    maxHeight?: number;
    quality?: number; // 0-100 for JPEG/WebP
    prioritizeCasThumbnails?: boolean;
    bypassCache?: boolean; // Skip cache lookup and storage
    includeTextures?: boolean;
    generateFallbacks?: boolean;
}

/**
 * Service for extracting thumbnails from Sims 4 mod packages
 */
export class ThumbnailExtractionService {
    private static readonly DEFAULT_OPTIONS: Required<ThumbnailExtractionOptions> = {
        maxThumbnails: 10,
        preferredFormat: 'webp',
        maxWidth: 256,
        maxHeight: 256,
        quality: 85,
        prioritizeCasThumbnails: true,
        bypassCache: false,
        includeTextures: true,
        generateFallbacks: true
    };

    /**
     * Get a human-readable name for a resource type
     */
    private static getResourceTypeName(resourceType: number): string {
        switch (resourceType) {
            case BinaryResourceType.CasPartThumbnail: return 'CAS Thumbnail';
            case BinaryResourceType.DdsImage: return 'DDS Image';
            case BinaryResourceType.DstImage: return 'DST Image';
            case BinaryResourceType.Rle2Image: return 'RLE2 Image';
            case BinaryResourceType.RlesImage: return 'RLES Image';
            case BinaryResourceType.PngImage: return 'PNG Image';
            default: return 'Unknown';
        }
    }

    /**
     * Extracts thumbnails from a mod package buffer
     */
    public static async extractThumbnails(
        packageBuffer: Buffer,
        fileName: string,
        options: ThumbnailExtractionOptions = {}
    ): Promise<ThumbnailExtractionResult> {
        console.log(`🔍 [ThumbnailExtraction] Starting extraction for: ${fileName}`);
        console.log(`📊 [ThumbnailExtraction] Package buffer size: ${packageBuffer.length} bytes`);
        console.log(`⚙️ [ThumbnailExtraction] Options:`, options);

        // Add global timeout to prevent app freezing
        return new Promise((resolve) => {
            const globalTimeout = setTimeout(() => {
                console.warn(`⏰ [ThumbnailExtraction] Global timeout reached for ${fileName}, returning fallback result`);
                resolve({
                    success: false,
                    thumbnails: [],
                    errors: ['Thumbnail extraction timeout - prevented app freeze'],
                    processingTime: 10000,
                    totalImagesFound: 0,
                    thumbnailsExtracted: 0
                });
            }, 10000); // 10 second global timeout

            // Run the actual extraction
            this.extractThumbnailsInternal(packageBuffer, fileName, options)
                .then((result) => {
                    clearTimeout(globalTimeout);
                    resolve(result);
                })
                .catch((error) => {
                    clearTimeout(globalTimeout);
                    console.error(`❌ [ThumbnailExtraction] Extraction failed for ${fileName}:`, error);
                    resolve({
                        success: false,
                        thumbnails: [],
                        errors: [error instanceof Error ? error.message : 'Unknown extraction error'],
                        processingTime: 0,
                        totalImagesFound: 0,
                        thumbnailsExtracted: 0
                    });
                });
        });
    }

    /**
     * Internal thumbnail extraction method (without timeout wrapper)
     */
    private static async extractThumbnailsInternal(
        packageBuffer: Buffer,
        fileName: string,
        options: ThumbnailExtractionOptions = {}
    ): Promise<ThumbnailExtractionResult> {
        const startTime = performance.now();
        const opts = { ...this.DEFAULT_OPTIONS, ...options };

        const result: ThumbnailExtractionResult = {
            success: false,
            thumbnails: [],
            errors: [],
            processingTime: 0,
            totalImagesFound: 0,
            thumbnailsExtracted: 0
        };

        // Since we're now only running in Electron main process, proceed with full extraction
        console.log('🔧 [ThumbnailExtraction] Running in Electron main process, proceeding with full extraction');

        // Generate file hash for cache validation (declare at top level for later use)
        console.log('🔧 [ThumbnailExtraction] Generating file hash...');
        const fileHash = this.generateFileHash(packageBuffer);
        console.log(`🔑 [ThumbnailExtraction] File hash: ${fileHash}`);

        // Node.js environment - proceed with full extraction
        try {

            // Check cache first (if available)
            console.log('💾 [ThumbnailExtraction] Checking cache...');
            if (ThumbnailCacheService) {
                const cacheService = ThumbnailCacheService.getInstance();
                const cachedThumbnail = await cacheService.getThumbnail(fileName, fileHash);

                if (cachedThumbnail && !opts.bypassCache) {
                    console.log('✅ [ThumbnailExtraction] Found cached thumbnail, returning early');
                    return {
                        success: true,
                        thumbnails: [cachedThumbnail],
                        errors: [],
                        processingTime: performance.now() - startTime,
                        totalImagesFound: 1,
                        thumbnailsExtracted: 1
                    };
                } else {
                    console.log('❌ [ThumbnailExtraction] No cached thumbnail found, proceeding with extraction');
                }
            } else {
                console.log('❌ [ThumbnailExtraction] ThumbnailCacheService not available');
            }
        } catch (cacheError) {
            console.warn('⚠️ [ThumbnailExtraction] Cache check failed, proceeding with extraction:', cacheError);
        }

        let parseResult: any;
        try {
            console.log('📦 [ThumbnailExtraction] Starting package parsing...');
            // Parse the package using shared service
            parseResult = PackageParserService.parsePackage(packageBuffer, fileName, {
                decompressBuffers: true,
                loadRaw: false,
                enableCaching: true
            });
            console.log('✅ [ThumbnailExtraction] Package parsing completed');

            if (!parseResult.package) {
                console.error('❌ [ThumbnailExtraction] Package parsing failed: No package returned');
                result.errors.push('Failed to parse package: No package returned');
                return result;
            }
            console.log(`✅ [ThumbnailExtraction] Package parsed successfully with ${parseResult.resourceCount} resources`);
        } catch (parseError) {
            console.error('❌ [ThumbnailExtraction] Package parsing threw exception:', parseError);
            result.errors.push(`Failed to parse package: ${parseError.message || parseError}`);
            return result;
        }

        try {

            // Find all image resources using shared resource groups
            const imageResources = PackageParserService.getResourcesByType(
                parseResult.package,
                RESOURCE_GROUPS.THUMBNAIL_RESOURCES
            );
            result.totalImagesFound = imageResources.length;

            console.log(`🎯 [ThumbnailExtraction] Found ${imageResources.length} image resources`);

            // Log resource types found
            const resourceTypeCounts = new Map<number, number>();
            imageResources.forEach(resource => {
                // Check different possible ways to access the resource type
                const resourceType = resource.type || resource.resourceType || resource.typeId ||
                                   (resource._key && resource._key.type) ||
                                   (resource.key && resource.key.type);
                if (resourceType !== undefined) {
                    const count = resourceTypeCounts.get(resourceType) || 0;
                    resourceTypeCounts.set(resourceType, count + 1);
                } else {
                    // Only log the first few to avoid spam
                    if (resourceTypeCounts.size < 3) {
                        console.warn('Resource has no accessible type:', Object.keys(resource));
                    }
                }
            });

            console.log('📊 [ThumbnailExtraction] Resource types found:');
            resourceTypeCounts.forEach((count, type) => {
                const typeHex = type ? type.toString(16) : 'undefined';
                const typeName = type ? ThumbnailExtractionService.getResourceTypeName(type) : 'Unknown';
                console.log(`  0x${typeHex}: ${count} resources (${typeName})`);
            });

            if (imageResources.length === 0) {
                // Generate fallback thumbnail based on package content
                if (opts.generateFallbacks) {
                    const fallbackThumbnail = this.generateFallbackThumbnail(parseResult.package, fileName, opts);
                    if (fallbackThumbnail) {
                        result.thumbnails.push(fallbackThumbnail);
                        result.thumbnailsExtracted = 1;
                        result.success = true;
                    }
                }

                if (result.thumbnails.length === 0) {
                    result.errors.push('No image resources found in package');
                }

                result.processingTime = performance.now() - startTime;
                return result;
            }

            // Sort resources by priority (CAS thumbnails first if preferred)
            const sortedResources = this.sortResourcesByPriority(imageResources, opts);

            // Extract thumbnails up to the limit
            const extractionPromises = sortedResources
                .slice(0, opts.maxThumbnails)
                .map(resource => this.extractThumbnailFromResource(resource, fileName, opts));

            const thumbnailResults = await Promise.allSettled(extractionPromises);

            // Process results
            for (const thumbnailResult of thumbnailResults) {
                if (thumbnailResult.status === 'fulfilled' && thumbnailResult.value) {
                    result.thumbnails.push(thumbnailResult.value);
                    result.thumbnailsExtracted++;
                } else if (thumbnailResult.status === 'rejected') {
                    result.errors.push(thumbnailResult.reason?.message || 'Unknown extraction error');
                }
            }

            // 🎯 SMART PRIMARY THUMBNAIL SELECTION
            // Reorganize thumbnails to ensure the best representative image is first
            if (result.thumbnails.length > 0) {
                result.thumbnails = this.selectPrimaryThumbnail(result.thumbnails, fileName);
                console.log(`🎨 [ThumbnailExtraction] Smart selection complete. Primary: ${result.thumbnails[0]?.resourceType}, Total: ${result.thumbnails.length}`);
            }

            // Cache successful extractions
            if (result.thumbnails.length > 0 && !opts.bypassCache && ThumbnailCacheService) {
                try {
                    const cacheService = ThumbnailCacheService.getInstance();
                    const primaryThumbnail = result.thumbnails[0];
                    await cacheService.cacheThumbnail(fileName, {
                        modFileName: primaryThumbnail.modFileName,
                        imageData: primaryThumbnail.imageData,
                        format: primaryThumbnail.format,
                        width: primaryThumbnail.width,
                        height: primaryThumbnail.height,
                        category: primaryThumbnail.category,
                        fileSize: primaryThumbnail.fileSize
                    }, fileHash);
                } catch (cacheError) {
                    console.warn('Failed to cache thumbnail:', cacheError);
                }
            }

            result.success = result.thumbnails.length > 0;
            result.processingTime = performance.now() - startTime;

            console.log(`✅ [ThumbnailExtraction] Completed for ${fileName}: ${result.thumbnailsExtracted}/${result.totalImagesFound} thumbnails extracted in ${result.processingTime.toFixed(2)}ms`);

            return result;

        } catch (error) {
            console.error('Thumbnail extraction failed:', error);
            result.errors.push(`Package parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);

            // If we're in browser environment and extraction failed, create fallback thumbnails
            if (typeof window !== 'undefined' && opts.generateFallbacks) {
                console.log('Creating browser-compatible fallback thumbnails...');
                const fallbackThumbnail = this.createBrowserFallbackThumbnail(fileName, opts);
                if (fallbackThumbnail) {
                    result.thumbnails.push(fallbackThumbnail);
                    result.thumbnailsExtracted = 1;
                    result.success = true;
                }
            }

            result.processingTime = performance.now() - startTime;
            return result;
        }
    }

    // Removed findImageResources and isImageResourceType methods - now using shared ResourceTypeRegistry

    /**
     * 🎯 SMART PRIMARY THUMBNAIL SELECTION
     * Intelligently selects the best representative thumbnail as primary
     * and organizes the rest as variations based on Sims 4 resource hierarchy
     */
    private static selectPrimaryThumbnail(thumbnails: ThumbnailData[], fileName: string): ThumbnailData[] {
        console.log(`🔍 [SmartSelection] Analyzing ${thumbnails.length} thumbnails for: ${fileName}`);

        // Log all thumbnail types for debugging
        thumbnails.forEach((thumb, index) => {
            console.log(`  ${index}: ${thumb.resourceType} (${thumb.extractionMethod}) - ${thumb.width}x${thumb.height} - ${thumb.confidence}% confidence`);
        });

        // 1. PRIORITY HIERARCHY for primary thumbnail selection
        const primaryPriority = [
            'CAS Thumbnail',           // Highest priority - actual representative image
            'PNG Image',               // High quality standalone images
            'DDS Image',               // High quality textures (if large enough)
            'RLE2 Image',              // Compressed thumbnails (often variations)
            'RLES Image',              // Compressed thumbnails (often variations)
            'DST Image',               // Lower priority textures
            'Generic Image',           // Fallback images
            'Fallback Icon'            // Last resort
        ];

        // 2. FIND BEST PRIMARY THUMBNAIL
        let primaryThumbnail: ThumbnailData | null = null;
        let primaryIndex = -1;

        for (const resourceType of primaryPriority) {
            const candidates = thumbnails
                .map((thumb, index) => ({ thumb, index }))
                .filter(({ thumb }) => thumb.resourceType === resourceType);

            if (candidates.length > 0) {
                // For CAS Thumbnails and PNG Images, prefer the first one (usually best quality)
                if (resourceType === 'CAS Thumbnail' || resourceType === 'PNG Image') {
                    primaryThumbnail = candidates[0].thumb;
                    primaryIndex = candidates[0].index;
                    console.log(`✅ [SmartSelection] Selected ${resourceType} as primary thumbnail`);
                    break;
                }

                // For other types, prefer larger, higher confidence thumbnails
                const bestCandidate = candidates.reduce((best, current) => {
                    const bestScore = this.calculateThumbnailScore(best.thumb);
                    const currentScore = this.calculateThumbnailScore(current.thumb);
                    return currentScore > bestScore ? current : best;
                });

                primaryThumbnail = bestCandidate.thumb;
                primaryIndex = bestCandidate.index;
                console.log(`✅ [SmartSelection] Selected best ${resourceType} as primary thumbnail (score: ${this.calculateThumbnailScore(primaryThumbnail)})`);
                break;
            }
        }

        // 3. ORGANIZE RESULTS
        if (primaryThumbnail && primaryIndex >= 0) {
            // Create new array with primary first, then variations
            const variations = thumbnails.filter((_, index) => index !== primaryIndex);
            const result = [primaryThumbnail, ...variations];

            console.log(`🎨 [SmartSelection] Final organization: Primary (${primaryThumbnail.resourceType}) + ${variations.length} variations`);
            return result;
        }

        // Fallback: return original order if no clear primary found
        console.warn(`⚠️ [SmartSelection] No clear primary thumbnail found, using original order`);
        return thumbnails;
    }

    /**
     * Calculates a quality score for thumbnail selection
     */
    private static calculateThumbnailScore(thumbnail: ThumbnailData): number {
        let score = 0;

        // Size score (larger is generally better for primary thumbnails)
        const pixelCount = thumbnail.width * thumbnail.height;
        score += Math.min(pixelCount / 1000, 50); // Max 50 points for size

        // Confidence score
        score += thumbnail.confidence * 0.5; // Max 50 points for confidence

        // Quality bonus
        if (thumbnail.isHighQuality) {
            score += 20;
        }

        // File size bonus (reasonable size indicates good quality)
        if (thumbnail.fileSize > 8192 && thumbnail.fileSize < 1048576) { // 8KB - 1MB sweet spot
            score += 10;
        }

        return score;
    }

    /**
     * Sorts resources by extraction priority
     */
    private static sortResourcesByPriority(
        resources: ResourceEntry[],
        options: Required<ThumbnailExtractionOptions>
    ): ResourceEntry[] {
        return resources.sort((a, b) => {
            const aPriority = this.getResourcePriority(a.key.type, options);
            const bPriority = this.getResourcePriority(b.key.type, options);
            return bPriority - aPriority; // Higher priority first
        });
    }

    /**
     * Gets priority score for a resource type
     * Updated to prioritize representative images over texture variations
     */
    private static getResourcePriority(resourceType: number, options: Required<ThumbnailExtractionOptions>): number {
        // 🎯 SMART EXTRACTION PRIORITY
        // Always prioritize CAS thumbnails as they are the actual representative images
        if (resourceType === BinaryResourceType.CasPartThumbnail) {
            return 100; // Highest priority - these are the main mod thumbnails
        }

        switch (resourceType) {
            case BinaryResourceType.PngImage: return 90;  // High quality standalone images
            case BinaryResourceType.DdsImage: return 80;  // High quality textures
            case BinaryResourceType.Rle2Image: return 70; // Compressed thumbnails (often variations)
            case BinaryResourceType.RlesImage: return 65; // Compressed thumbnails (often variations)
            case BinaryResourceType.DstImage: return 60;  // Lower priority textures
            default: return 0;
        }
    }

    /**
     * Extracts a thumbnail from a specific resource
     */
    private static async extractThumbnailFromResource(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        const resourceType = resource.key.type;
        
        try {
            console.log(`🔍 [ThumbnailExtraction] Attempting to extract from resource type: 0x${resourceType.toString(16)} (${ThumbnailExtractionService.getResourceTypeName(resourceType)})`);

            switch (resourceType) {
                case BinaryResourceType.CasPartThumbnail:
                    console.log('📸 [ThumbnailExtraction] Using CAS Thumbnail extraction');
                    return await this.extractFromCasThumbnail(resource, fileName, options);

                case BinaryResourceType.DdsImage:
                    console.log('🖼️ [ThumbnailExtraction] Using DDS Image extraction');
                    return await this.extractFromDdsImage(resource, fileName, options);

                case BinaryResourceType.DstImage:
                    console.log('🎨 [ThumbnailExtraction] Using DST Image extraction');
                    return await this.extractFromDstImage(resource, fileName, options);

                case BinaryResourceType.Rle2Image:
                    console.log('🎯 [ThumbnailExtraction] Using RLE2 Image extraction');
                    return await this.extractFromRle2Image(resource, fileName, options);

                case BinaryResourceType.RlesImage:
                    console.log('🎪 [ThumbnailExtraction] Using RLES Image extraction');
                    return await this.extractFromRlesImage(resource, fileName, options);

                case BinaryResourceType.PngImage:
                    console.log('🖼️ [ThumbnailExtraction] Using PNG Image extraction');
                    return await this.extractFromPngImage(resource, fileName, options);

                default:
                    console.log(`❓ [ThumbnailExtraction] Unknown resource type 0x${resourceType.toString(16)}, using generic extraction`);
                    if (options.generateFallbacks) {
                        return await this.extractGenericImage(resource, fileName, options);
                    }
                    return null;
            }
        } catch (error) {
            console.warn(`Failed to extract thumbnail from resource ${resource.key.type}:`, error);
            return null;
        }
    }

    /**
     * Extracts thumbnail from CAS Part Thumbnail resource
     */
    private static async extractFromCasThumbnail(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        // CAS thumbnails are typically already in a usable format
        // Extract buffer from S4TK resource
        const buffer = this.extractBufferFromResource(resource);
        if (!buffer) {
            console.warn('Failed to extract buffer from CAS thumbnail resource');
            return null;
        }

        const imageData = this.bufferToBase64(buffer, 'image/png');
        
        return {
            id: `${fileName}-cas-${resource.key.instance.toString(16)}`,
            modFileName: fileName,
            resourceType: 'CAS Thumbnail',
            resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
            imageData,
            format: 'png',
            width: 256, // Standard CAS thumbnail size
            height: 256,
            category: 'Create-a-Sim',
            confidence: 95,
            extractionMethod: 'cas_thumbnail',
            fileSize: buffer.length,
            isHighQuality: true
        };
    }

    /**
     * Extracts thumbnail from DDS Image resource
     */
    private static async extractFromDdsImage(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        try {
            const buffer = this.extractBufferFromResource(resource);
            if (!buffer) {
                console.warn('Failed to extract buffer from DDS resource');
                return null;
            }

            // Try to parse with S4TK first for metadata
            let width = 256;
            let height = 256;
            let confidence = 85;

            try {
                const ddsResource = DdsImageResource.from(buffer);
                const imageInfo = ddsResource.image;

                if (imageInfo) {
                    width = imageInfo.width || 256;
                    height = imageInfo.height || 256;
                }
            } catch (s4tkError) {
                console.warn('S4TK DDS parsing failed, trying direct conversion:', s4tkError);
                confidence = 70; // Lower confidence if S4TK parsing failed
            }

            // Convert DDS to web-compatible format using the raw buffer
            const imageData = this.convertDdsBufferToWebFormat(buffer, options);

            return {
                id: `${fileName}-dds-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'DDS Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData,
                format: options.preferredFormat,
                width,
                height,
                category: 'Texture',
                confidence,
                extractionMethod: 'dds_decode_conversion',
                fileSize: buffer.length,
                isHighQuality: width >= 256 && height >= 256
            };
        } catch (error) {
            console.warn('Failed to parse DDS image:', error);
            return null;
        }
    }

    /**
     * Extracts thumbnail from PNG Image resource
     */
    private static async extractFromPngImage(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        const buffer = this.extractBufferFromResource(resource);
        if (!buffer) {
            console.warn('Failed to extract buffer from PNG resource');
            return null;
        }

        const imageData = this.bufferToBase64(buffer, 'image/png');
        
        return {
            id: `${fileName}-png-${resource.key.instance.toString(16)}`,
            modFileName: fileName,
            resourceType: 'PNG Image',
            resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
            imageData,
            format: 'png',
            width: 256, // Will be determined by actual image analysis
            height: 256,
            category: 'Image',
            confidence: 90,
            extractionMethod: 'png_direct',
            fileSize: buffer.length,
            isHighQuality: true
        };
    }

    /**
     * Extracts thumbnail from DST Image resource
     */
    private static async extractFromDstImage(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        try {
            // DST images are "shuffled" DDS images used by Sims 4
            // Use S4TK DdsImage to decode them properly
            const buffer = this.extractBufferFromResource(resource);
            if (!buffer) {
                console.warn('Failed to extract buffer from DST resource');
                return null;
            }

            // Validate buffer size before processing
            if (buffer.length < 128) { // Minimum reasonable size for a DST image
                console.warn('DST buffer too small, skipping:', buffer.length, 'bytes');
                return null;
            }

            // Add timeout and error boundaries for S4TK processing to prevent freezing
            const processWithTimeout = () => {
                return new Promise<any>((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('DST processing timeout - preventing app freeze'));
                    }, 3000); // 3 second timeout

                    try {
                        // Validate DXT buffer structure before S4TK processing
                        if (!this.validateDxtBufferStructure(buffer)) {
                            throw new Error('Invalid DXT buffer structure - preventing range error');
                        }

                        // Try to decode using S4TK DdsImage with additional validation
                        const ddsImage = DdsImage.from(buffer);

                        // Validate the decoded image
                        if (!ddsImage) {
                            throw new Error('DdsImage.from returned null');
                        }

                        if (ddsImage.isShuffled) {
                            console.log('✅ Successfully detected shuffled DST image');
                        }

                        clearTimeout(timeout);
                        resolve(ddsImage);
                    } catch (error) {
                        clearTimeout(timeout);
                        reject(error);
                    }
                });
            };

            const ddsImage = await processWithTimeout();

            // Convert to Jimp with additional error handling and validation
            let jimpImage;
            try {
                jimpImage = ddsImage.toJimp();
                if (!jimpImage || jimpImage.getWidth() <= 0 || jimpImage.getHeight() <= 0) {
                    throw new Error('Invalid Jimp image dimensions');
                }
            } catch (jimpError) {
                console.warn('Failed to convert DST to Jimp (range error prevention):', jimpError);
                throw jimpError; // Re-throw to trigger fallback
            }

            const pngBuffer = await jimpImage.getBufferAsync('image/png');
            const base64Data = `data:image/png;base64,${pngBuffer.toString('base64')}`;

            return {
                id: `${fileName}-dst-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'DST Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData: base64Data,
                format: 'png',
                width: jimpImage.getWidth(),
                height: jimpImage.getHeight(),
                category: 'Thumbnail',
                confidence: 90,
                extractionMethod: 'dst_conversion',
                fileSize: buffer.length,
                isHighQuality: buffer.length >= 32768 // 32KB+ considered high quality
            };
        } catch (error) {
            console.warn('Failed to parse DST image with S4TK, using fallback:', error);

            // Fallback to generic icon
            const buffer = this.extractBufferFromResource(resource);
            return {
                id: `${fileName}-dst-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'DST Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData: this.generateTextureIcon(256, 256, 'DST Texture'),
                format: 'svg',
                width: 256,
                height: 256,
                category: 'Texture',
                confidence: 30,
                extractionMethod: 'dst_conversion',
                fileSize: buffer?.length || 0,
                isHighQuality: false,
                isFallback: true
            };
        }
    }

    /**
     * Extracts thumbnail from RLE2 Image resource
     */
    private static async extractFromRle2Image(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        try {
            // RLE2 images are compressed DDS images commonly used in Sims 4 CC thumbnails
            // Use S4TK DdsImage to decode them properly
            const buffer = this.extractBufferFromResource(resource);
            if (!buffer) {
                console.warn('Failed to extract buffer from RLE2 resource');
                return null;
            }

            // RLE2 images are zlib-compressed DXT texture data
            // Try to decompress first, then decode with S4TK DdsImage
            let decompressedBuffer: Buffer;
            try {
                // Check if it starts with zlib header (0x78, 0xDA)
                if (buffer.length >= 2 && buffer[0] === 0x78 && buffer[1] === 0xDA) {
                    decompressedBuffer = zlib.inflateSync(buffer);
                    console.log('✅ Successfully decompressed RLE2 data');
                } else {
                    // Not compressed, use as-is
                    decompressedBuffer = buffer;
                }
            } catch (zlibError) {
                console.warn('Failed to decompress RLE2 data, using raw buffer:', zlibError);
                decompressedBuffer = buffer;
            }

            // The decompressed buffer contains raw DXT texture data without DDS headers
            // We need to construct a proper DDS file with headers for S4TK to process
            const ddsBuffer = this.constructDdsFromRawDxtData(decompressedBuffer);

            // Add timeout and error boundaries for S4TK processing to prevent freezing
            const processWithTimeout = () => {
                return new Promise<any>((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('RLE2 processing timeout - preventing app freeze'));
                    }, 3000); // 3 second timeout

                    try {
                        // Validate DXT buffer structure before S4TK processing
                        if (!this.validateDxtBufferStructure(ddsBuffer)) {
                            throw new Error('Invalid constructed DDS buffer structure for RLE2 - preventing range error');
                        }

                        // Try to decode using S4TK DdsImage
                        const ddsImage = DdsImage.from(ddsBuffer);

                        // Validate the decoded image
                        if (!ddsImage) {
                            throw new Error('DdsImage.from returned null for RLE2');
                        }

                        console.log('✅ Successfully decoded RLE2 image with constructed DDS headers');
                        clearTimeout(timeout);
                        resolve(ddsImage);
                    } catch (error) {
                        clearTimeout(timeout);
                        reject(error);
                    }
                });
            };

            const ddsImage = await processWithTimeout();

            // Convert to Jimp with additional error handling and validation
            let jimpImage;
            try {
                jimpImage = ddsImage.toJimp();
                if (!jimpImage || jimpImage.getWidth() <= 0 || jimpImage.getHeight() <= 0) {
                    throw new Error('Invalid Jimp image dimensions for RLE2');
                }
            } catch (jimpError) {
                console.warn('Failed to convert RLE2 to Jimp (range error prevention):', jimpError);
                throw jimpError; // Re-throw to trigger fallback
            }

            const pngBuffer = await jimpImage.getBufferAsync('image/png');
            const base64Data = `data:image/png;base64,${pngBuffer.toString('base64')}`;

            return {
                id: `${fileName}-rle2-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'RLE2 Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData: base64Data,
                format: 'png',
                width: jimpImage.getWidth(),
                height: jimpImage.getHeight(),
                category: 'Thumbnail',
                confidence: 95, // High confidence for RLE2 thumbnails
                extractionMethod: 'rle2_conversion',
                fileSize: buffer.length,
                isHighQuality: buffer.length >= 16384 // 16KB+ considered high quality for thumbnails
            };
        } catch (error) {
            console.warn('Failed to parse RLE2 image with S4TK, using fallback:', error);

            // Fallback to generic icon
            const buffer = this.extractBufferFromResource(resource);
            return {
                id: `${fileName}-rle2-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'RLE2 Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData: this.generateTextureIcon(256, 256, 'RLE2 Image'),
                format: 'svg',
                width: 256,
                height: 256,
                category: 'Texture',
                confidence: 30,
                extractionMethod: 'rle2_conversion',
                fileSize: buffer?.length || 0,
                isHighQuality: false,
                isFallback: true
            };
        }
    }

    /**
     * Extracts thumbnail from RLES Image resource
     */
    private static async extractFromRlesImage(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        try {
            // RLES images are compressed DDS images used in Sims 4 CC (often specular maps)
            // Use S4TK DdsImage to decode them properly
            const buffer = this.extractBufferFromResource(resource);
            if (!buffer) {
                console.warn('Failed to extract buffer from RLES resource');
                return null;
            }

            // Try to decode using S4TK DdsImage
            const ddsImage = DdsImage.from(buffer);
            console.log('✅ Successfully decoded RLES image');

            // Convert to Jimp and then to PNG
            const jimpImage = ddsImage.toJimp();
            const pngBuffer = await jimpImage.getBufferAsync('image/png');
            const base64Data = `data:image/png;base64,${pngBuffer.toString('base64')}`;

            return {
                id: `${fileName}-rles-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'RLES Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData: base64Data,
                format: 'png',
                width: jimpImage.getWidth(),
                height: jimpImage.getHeight(),
                category: 'Thumbnail',
                confidence: 85, // Good confidence for RLES thumbnails
                extractionMethod: 'rles_conversion',
                fileSize: buffer.length,
                isHighQuality: buffer.length >= 16384 // 16KB+ considered high quality for thumbnails
            };
        } catch (error) {
            console.warn('Failed to parse RLES image with S4TK, using fallback:', error);

            // Fallback to generic icon
            const buffer = this.extractBufferFromResource(resource);
            return {
                id: `${fileName}-rles-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'RLES Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData: this.generateTextureIcon(256, 256, 'RLES Image'),
                format: 'svg',
                width: 256,
                height: 256,
                category: 'Texture',
                confidence: 30,
                extractionMethod: 'rles_conversion',
                fileSize: buffer?.length || 0,
                isHighQuality: false,
                isFallback: true
            };
        }
    }

    /**
     * Generic image extraction for other formats
     */
    private static async extractGenericImage(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        try {
            // Try to extract as raw image data first
            const buffer = resource.value as Buffer;

            // Check if it's a recognizable image format by magic bytes
            if (this.isPngBuffer(buffer)) {
                return this.extractFromPngImage(resource, fileName, options);
            }

            // For other formats, generate a category-based fallback
            const category = this.inferCategoryFromResource(resource);
            const imageData = this.generateCategoryIcon(category, fileName);

            return {
                id: `${fileName}-generic-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'Generic Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData,
                format: 'svg',
                width: 256,
                height: 256,
                category,
                confidence: 60,
                extractionMethod: 'category_fallback',
                fileSize: buffer.length,
                isHighQuality: false
            };
        } catch (error) {
            console.warn('Generic image extraction failed:', error);
            return null;
        }
    }

    /**
     * Checks if buffer contains PNG data
     */
    private static isPngBuffer(buffer: Buffer): boolean {
        return buffer.length >= 8 &&
               buffer[0] === 0x89 && buffer[1] === 0x50 &&
               buffer[2] === 0x4E && buffer[3] === 0x47;
    }

    /**
     * Infers category from resource type and context
     */
    private static inferCategoryFromResource(resource: ResourceEntry): string {
        const resourceType = resource.key.type;

        // Map resource types to categories
        if (resourceType === BinaryResourceType.CasPart ||
            resourceType === BinaryResourceType.CasPartThumbnail) {
            return 'CAS Content';
        }

        if (resourceType === BinaryResourceType.ObjectDefinition) {
            return 'Objects';
        }

        if (resourceType === BinaryResourceType.DdsImage ||
            resourceType === BinaryResourceType.DstImage ||
            resourceType === BinaryResourceType.Rle2Image ||
            resourceType === BinaryResourceType.RlesImage ||
            resourceType === BinaryResourceType.PngImage) {
            return 'Texture';
        }

        return 'Unknown';
    }

    /**
     * Generates fallback thumbnail based on package content analysis
     */
    private static generateFallbackThumbnail(
        s4tkPackage: Package,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): ThumbnailData | null {
        try {
            // Analyze package content to determine category
            const category = this.analyzePackageCategory(s4tkPackage);
            const imageData = this.generateCategoryIcon(category, fileName);

            return {
                id: `${fileName}-fallback-${Date.now()}`,
                modFileName: fileName,
                resourceType: 'Fallback Icon',
                resourceKey: 'fallback',
                imageData,
                format: 'svg',
                width: 256,
                height: 256,
                category,
                confidence: 40,
                extractionMethod: 'content_analysis_fallback',
                fileSize: 0,
                isHighQuality: false
            };
        } catch (error) {
            console.warn('Failed to generate fallback thumbnail:', error);
            return null;
        }
    }

    /**
     * Analyzes package content to determine the most appropriate category
     */
    private static analyzePackageCategory(s4tkPackage: Package): string {
        const resourceCounts = new Map<string, number>();

        // Count different resource types
        for (const entry of s4tkPackage.entries) {
            const resourceType = entry.key.type;

            if (resourceType === BinaryResourceType.CasPart ||
                resourceType === BinaryResourceType.CasPartThumbnail) {
                resourceCounts.set('CAS Content', (resourceCounts.get('CAS Content') || 0) + 1);
            } else if (resourceType === BinaryResourceType.ObjectDefinition) {
                resourceCounts.set('Objects', (resourceCounts.get('Objects') || 0) + 1);
            } else if (resourceType === BinaryResourceType.Script ||
                       resourceType === BinaryResourceType.PythonBytecode) {
                resourceCounts.set('Script Mod', (resourceCounts.get('Script Mod') || 0) + 1);
            } else if (resourceType === BinaryResourceType.DdsImage ||
                       resourceType === BinaryResourceType.DstImage ||
                       resourceType === BinaryResourceType.Rle2Image ||
                       resourceType === BinaryResourceType.RlesImage ||
                       resourceType === BinaryResourceType.PngImage) {
                resourceCounts.set('Texture', (resourceCounts.get('Texture') || 0) + 1);
            }
        }

        // Return the category with the most resources
        let maxCount = 0;
        let primaryCategory = 'Unknown';

        for (const [category, count] of resourceCounts) {
            if (count > maxCount) {
                maxCount = count;
                primaryCategory = category;
            }
        }

        return primaryCategory;
    }

    /**
     * Validates DXT buffer structure to prevent range errors during decompression
     * Based on research: DXT textures work with 4x4 pixel blocks
     * DXT1: 8 bytes per 4x4 block, DXT5: 16 bytes per 4x4 block
     */
    private static validateDxtBufferStructure(buffer: Buffer): boolean {
        try {
            // Minimum DDS header size is 128 bytes (DDS_HEADER + DDS_PIXELFORMAT)
            if (buffer.length < 128) {
                console.warn('DXT buffer too small for DDS header:', buffer.length);
                return false;
            }

            // Check for DDS magic number "DDS " (0x20534444)
            const magic = buffer.readUInt32LE(0);
            if (magic !== 0x20534444) {
                console.warn('Invalid DDS magic number:', magic.toString(16));
                return false;
            }

            // Read header size (should be 124)
            const headerSize = buffer.readUInt32LE(4);
            if (headerSize !== 124) {
                console.warn('Invalid DDS header size:', headerSize);
                return false;
            }

            // Read width and height from DDS header
            const height = buffer.readUInt32LE(12);
            const width = buffer.readUInt32LE(16);

            // Validate dimensions are reasonable
            if (width <= 0 || height <= 0 || width > 4096 || height > 4096) {
                console.warn('Invalid DDS dimensions:', width, 'x', height);
                return false;
            }

            // Calculate expected buffer size for DXT compression
            // DXT works with 4x4 pixel blocks
            const blocksWide = Math.max(1, Math.ceil(width / 4));
            const blocksHigh = Math.max(1, Math.ceil(height / 4));

            // Read pixel format to determine DXT type
            const pixelFormatOffset = 76; // DDS_PIXELFORMAT starts at offset 76
            const fourCC = buffer.readUInt32LE(pixelFormatOffset + 4);

            let bytesPerBlock = 8; // Default to DXT1
            let formatName = 'DXT1';

            // Check FourCC codes for DXT formats
            if (fourCC === 0x31545844) { // 'DXT1'
                bytesPerBlock = 8;
                formatName = 'DXT1';
            } else if (fourCC === 0x33545844) { // 'DXT3'
                bytesPerBlock = 16;
                formatName = 'DXT3';
            } else if (fourCC === 0x35545844) { // 'DXT5'
                bytesPerBlock = 16;
                formatName = 'DXT5';
            } else {
                // Unknown format, use conservative estimate
                console.warn('Unknown DXT format, FourCC:', fourCC.toString(16));
                bytesPerBlock = 16; // Use larger size to be safe
                formatName = 'Unknown';
            }

            const expectedDataSize = blocksWide * blocksHigh * bytesPerBlock;
            const headerAndDataSize = 128 + expectedDataSize; // Header + texture data

            // Allow some tolerance for additional data (mipmaps, etc.)
            const tolerance = 1024; // 1KB tolerance

            if (buffer.length < expectedDataSize || buffer.length > headerAndDataSize + tolerance) {
                console.warn(`DXT buffer size validation failed for ${formatName}:`);
                console.warn(`  Dimensions: ${width}x${height}`);
                console.warn(`  Blocks: ${blocksWide}x${blocksHigh}`);
                console.warn(`  Expected data size: ${expectedDataSize} bytes`);
                console.warn(`  Expected total size: ${headerAndDataSize} bytes`);
                console.warn(`  Actual buffer size: ${buffer.length} bytes`);

                // If the buffer is significantly smaller than expected, reject it
                if (buffer.length < expectedDataSize * 0.8) {
                    return false;
                }
            }

            console.log(`✅ DXT buffer validation passed: ${formatName} ${width}x${height}, ${buffer.length} bytes`);
            return true;

        } catch (error) {
            console.warn('DXT buffer validation error:', error);
            return false;
        }
    }

    /**
     * Constructs a complete DDS file from raw DXT texture data
     * This is needed for RLE2 images which contain only the compressed texture data without DDS headers
     */
    private static constructDdsFromRawDxtData(dxtData: Buffer): Buffer {
        // Assume DXT5 format for Sims 4 RLE2 images (most common for CC thumbnails)
        // Standard thumbnail dimensions for Sims 4 CC
        const width = 256;
        const height = 256;

        // Calculate linear size for DXT5: (width/4) * (height/4) * 16 bytes per block
        const blocksWide = Math.max(1, Math.floor((width + 3) / 4));
        const blocksHigh = Math.max(1, Math.floor((height + 3) / 4));
        const linearSize = blocksWide * blocksHigh * 16; // DXT5 uses 16 bytes per 4x4 block

        // Create DDS file buffer: 4 bytes magic + 124 bytes header + texture data
        const totalSize = 4 + 124 + dxtData.length;
        const ddsBuffer = Buffer.alloc(totalSize);

        let offset = 0;

        // Write DDS magic number "DDS " (0x20534444)
        ddsBuffer.writeUInt32LE(0x20534444, offset);
        offset += 4;

        // Write DDS_HEADER (124 bytes)
        // dwSize (must be 124)
        ddsBuffer.writeUInt32LE(124, offset);
        offset += 4;

        // dwFlags (required flags for DXT5 texture)
        const flags = 0x1 | 0x2 | 0x4 | 0x1000 | 0x80000; // CAPS | HEIGHT | WIDTH | PIXELFORMAT | LINEARSIZE
        ddsBuffer.writeUInt32LE(flags, offset);
        offset += 4;

        // dwHeight
        ddsBuffer.writeUInt32LE(height, offset);
        offset += 4;

        // dwWidth
        ddsBuffer.writeUInt32LE(width, offset);
        offset += 4;

        // dwPitchOrLinearSize (linear size for compressed texture)
        ddsBuffer.writeUInt32LE(linearSize, offset);
        offset += 4;

        // dwDepth (unused for 2D texture)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwMipMapCount (1 for single mip level)
        ddsBuffer.writeUInt32LE(1, offset);
        offset += 4;

        // dwReserved1[11] (44 bytes of zeros)
        for (let i = 0; i < 11; i++) {
            ddsBuffer.writeUInt32LE(0, offset);
            offset += 4;
        }

        // DDS_PIXELFORMAT (32 bytes)
        // dwSize (must be 32)
        ddsBuffer.writeUInt32LE(32, offset);
        offset += 4;

        // dwFlags (DDPF_FOURCC for compressed format)
        ddsBuffer.writeUInt32LE(0x4, offset);
        offset += 4;

        // dwFourCC ("DXT5" = 0x35545844)
        ddsBuffer.writeUInt32LE(0x35545844, offset);
        offset += 4;

        // dwRGBBitCount (unused for compressed format)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwRBitMask (unused for compressed format)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwGBitMask (unused for compressed format)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwBBitMask (unused for compressed format)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwABitMask (unused for compressed format)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwCaps (DDSCAPS_TEXTURE)
        ddsBuffer.writeUInt32LE(0x1000, offset);
        offset += 4;

        // dwCaps2 (unused)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwCaps3 (unused)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwCaps4 (unused)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // dwReserved2 (unused)
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;

        // Copy the raw DXT texture data
        dxtData.copy(ddsBuffer, offset);

        console.log(`🔧 [RLE2] Constructed DDS file: ${width}x${height} DXT5, ${dxtData.length} bytes texture data, ${totalSize} bytes total`);

        return ddsBuffer;
    }

    /**
     * Extracts Buffer from S4TK resource
     */
    private static extractBufferFromResource(resource: ResourceEntry): Buffer | null {
        try {
            // Check if resource.value is already a Buffer
            if (resource.value instanceof Buffer) {
                return resource.value;
            }

            // Check if it's an S4TK resource with buffer cache
            if (resource.value && typeof resource.value === 'object') {
                const rawResource = resource.value as any;

                // Try to access the buffer from various S4TK resource structures
                if (rawResource._bufferCache?.buffer instanceof Buffer) {
                    return rawResource._bufferCache.buffer;
                }

                if (rawResource.buffer instanceof Buffer) {
                    return rawResource.buffer;
                }

                // Try to get the buffer using S4TK's buffer property
                if (typeof rawResource.getBuffer === 'function') {
                    return rawResource.getBuffer();
                }
            }

            console.warn('Could not extract buffer from resource:', typeof resource.value);
            return null;
        } catch (error) {
            console.warn('Error extracting buffer from resource:', error);
            return null;
        }
    }

    /**
     * Converts a buffer to base64 data URL
     */
    private static bufferToBase64(buffer: Buffer, mimeType: string): string {
        const base64 = buffer.toString('base64');
        return `data:${mimeType};base64,${base64}`;
    }

    /**
     * Converts DDS image data to web-compatible format
     */
    private static convertDdsToWebFormat(imageInfo: any, options: Required<ThumbnailExtractionOptions>): string {
        try {
            // Check if we have raw DDS buffer data
            if (imageInfo && imageInfo.buffer) {
                return this.convertDdsBufferToWebFormat(imageInfo.buffer, options);
            }

            // Check if we have pixel data available
            if (imageInfo && imageInfo.data && imageInfo.width && imageInfo.height) {
                return this.convertPixelDataToWebFormat(imageInfo, options);
            }

            // If no pixel data, try to extract basic info and create a representative icon
            const width = imageInfo?.width || 256;
            const height = imageInfo?.height || 256;

            return this.generateTextureIcon(width, height, 'DDS Texture');
        } catch (error) {
            console.warn('DDS conversion failed, using fallback:', error);
            return this.generateTextureIcon(256, 256, 'DDS Texture');
        }
    }

    /**
     * Converts DDS buffer to web format using decode-dxt and parse-dds libraries
     */
    private static convertDdsBufferToWebFormat(buffer: Buffer, options: Required<ThumbnailExtractionOptions>): string {
        try {
            // Check if DDS processing libraries are available (Node.js environment)
            if (!decodeDXT || !parseDDS) {
                console.warn('DDS processing libraries not available in browser environment, using fallback');
                return this.generateTextureIcon(256, 256, 'DDS Texture');
            }

            // Convert Buffer to Uint8Array for the DDS parser
            const uint8Array = new Uint8Array(buffer);

            // Parse the DDS file
            const ddsData = parseDDS(uint8Array);

            if (!ddsData || !ddsData.images || ddsData.images.length === 0) {
                throw new Error('No image data found in DDS file');
            }

            // Get the first mipmap level
            const image = ddsData.images[0];
            const imageWidth = image.shape[0];
            const imageHeight = image.shape[1];

            // Create DataView for the image data
            const imageDataView = new DataView(uint8Array.buffer, image.offset, image.length);

            // Decode the DXT texture to RGBA data
            let rgbaData: Uint8Array;

            switch (ddsData.format) {
                case 'dxt1':
                    rgbaData = decodeDXT(imageDataView, imageWidth, imageHeight, decodeDXT.dxt1);
                    break;
                case 'dxt3':
                    rgbaData = decodeDXT(imageDataView, imageWidth, imageHeight, decodeDXT.dxt3);
                    break;
                case 'dxt5':
                    rgbaData = decodeDXT(imageDataView, imageWidth, imageHeight, decodeDXT.dxt5);
                    break;
                default:
                    throw new Error(`Unsupported DDS format: ${ddsData.format}`);
            }

            // Convert RGBA data to Canvas and then to web format
            return this.convertRgbaToWebFormat(rgbaData, imageWidth, imageHeight, options);

        } catch (error) {
            console.warn('DDS buffer conversion failed:', error);
            return this.generateTextureIcon(256, 256, 'DDS Texture');
        }
    }

    /**
     * Converts RGBA data to web format using Canvas
     */
    private static convertRgbaToWebFormat(
        rgbaData: Uint8Array,
        width: number,
        height: number,
        options: Required<ThumbnailExtractionOptions>
    ): string {
        try {
            // Create a canvas element
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                throw new Error('Could not get canvas context');
            }

            // Resize if needed
            const targetWidth = Math.min(width, options.maxWidth);
            const targetHeight = Math.min(height, options.maxHeight);

            canvas.width = targetWidth;
            canvas.height = targetHeight;

            // Create ImageData from the RGBA buffer
            const imageData = ctx.createImageData(targetWidth, targetHeight);

            // If we need to resize, we'll need to sample the data
            if (targetWidth !== width || targetHeight !== height) {
                this.resampleRgbaData(rgbaData, width, height, imageData.data, targetWidth, targetHeight);
            } else {
                // Direct copy
                imageData.data.set(rgbaData);
            }

            // Put the image data on canvas
            ctx.putImageData(imageData, 0, 0);

            // Convert to desired format
            const mimeType = this.getMimeType(options.preferredFormat);
            const quality = options.preferredFormat === 'png' ? undefined : options.quality / 100;

            return canvas.toDataURL(mimeType, quality);
        } catch (error) {
            console.warn('RGBA data conversion failed:', error);
            return this.generateTextureIcon(256, 256, 'DDS Texture');
        }
    }

    /**
     * Converts raw pixel data to web format using Canvas (legacy method)
     */
    private static convertPixelDataToWebFormat(imageInfo: any, options: Required<ThumbnailExtractionOptions>): string {
        try {
            const width = imageInfo.width;
            const height = imageInfo.height;
            const pixelData = imageInfo.data;

            return this.convertRgbaToWebFormat(pixelData, width, height, options);
        } catch (error) {
            console.warn('Pixel data conversion failed:', error);
            return this.generateTextureIcon(256, 256, 'DDS Texture');
        }
    }

    /**
     * Simple bilinear resampling for RGBA data
     */
    private static resampleRgbaData(
        srcData: Uint8Array,
        srcWidth: number,
        srcHeight: number,
        dstData: Uint8ClampedArray,
        dstWidth: number,
        dstHeight: number
    ): void {
        const xRatio = srcWidth / dstWidth;
        const yRatio = srcHeight / dstHeight;

        for (let y = 0; y < dstHeight; y++) {
            for (let x = 0; x < dstWidth; x++) {
                const srcX = Math.floor(x * xRatio);
                const srcY = Math.floor(y * yRatio);

                const srcIndex = (srcY * srcWidth + srcX) * 4;
                const dstIndex = (y * dstWidth + x) * 4;

                // Copy RGBA values
                dstData[dstIndex] = srcData[srcIndex];         // R
                dstData[dstIndex + 1] = srcData[srcIndex + 1]; // G
                dstData[dstIndex + 2] = srcData[srcIndex + 2]; // B
                dstData[dstIndex + 3] = srcData[srcIndex + 3]; // A
            }
        }
    }

    /**
     * Generates a category-based icon for textures
     */
    private static generateTextureIcon(width: number, height: number, label: string): string {
        const iconSvg = `<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="textureGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="256" height="256" fill="url(#textureGrad)" rx="8"/>
            <rect x="32" y="32" width="192" height="192" fill="none" stroke="white" stroke-width="2" stroke-dasharray="8,4" opacity="0.7"/>
            <circle cx="128" cy="100" r="24" fill="white" opacity="0.8"/>
            <text x="128" y="140" text-anchor="middle" fill="white" font-family="system-ui, -apple-system, sans-serif" font-size="14" font-weight="500">${label}</text>
            <text x="128" y="160" text-anchor="middle" fill="white" font-family="system-ui, -apple-system, sans-serif" font-size="12" opacity="0.8">${width}×${height}</text>
        </svg>`;

        return `data:image/svg+xml;base64,${Buffer.from(iconSvg).toString('base64')}`;
    }

    /**
     * Gets MIME type for preferred format
     */
    private static getMimeType(format: string): string {
        switch (format) {
            case 'webp': return 'image/webp';
            case 'jpg':
            case 'jpeg': return 'image/jpeg';
            case 'png':
            default: return 'image/png';
        }
    }

    /**
     * Generates category-based fallback icons
     */
    private static generateCategoryIcon(category: string, modFileName: string): string {
        const iconConfig = this.getCategoryIconConfig(category);

        const iconSvg = `<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="categoryGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${iconConfig.primaryColor};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${iconConfig.secondaryColor};stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="256" height="256" fill="url(#categoryGrad)" rx="12"/>
            <g transform="translate(128, 100)">
                ${iconConfig.icon}
            </g>
            <text x="128" y="160" text-anchor="middle" fill="white" font-family="system-ui, -apple-system, sans-serif" font-size="14" font-weight="600">${iconConfig.label}</text>
            <text x="128" y="180" text-anchor="middle" fill="white" font-family="system-ui, -apple-system, sans-serif" font-size="11" opacity="0.9">${this.truncateFileName(modFileName)}</text>
        </svg>`;

        return `data:image/svg+xml;base64,${Buffer.from(iconSvg).toString('base64')}`;
    }

    /**
     * Gets icon configuration for different mod categories
     */
    private static getCategoryIconConfig(category: string): { primaryColor: string; secondaryColor: string; icon: string; label: string } {
        const lowerCategory = category.toLowerCase();

        if (lowerCategory.includes('cas') || lowerCategory.includes('clothing') || lowerCategory.includes('hair')) {
            return {
                primaryColor: '#EC4899',
                secondaryColor: '#BE185D',
                icon: '<circle cx="0" cy="-10" r="20" fill="white" opacity="0.9"/><rect x="-15" y="5" width="30" height="25" fill="white" opacity="0.9" rx="4"/>',
                label: 'CAS Content'
            };
        }

        if (lowerCategory.includes('object') || lowerCategory.includes('furniture') || lowerCategory.includes('build')) {
            return {
                primaryColor: '#059669',
                secondaryColor: '#047857',
                icon: '<rect x="-20" y="-15" width="40" height="30" fill="white" opacity="0.9" rx="4"/><rect x="-15" y="-10" width="30" height="20" fill="none" stroke="white" stroke-width="2" opacity="0.7"/>',
                label: 'Objects'
            };
        }

        if (lowerCategory.includes('script') || lowerCategory.includes('mod')) {
            return {
                primaryColor: '#7C3AED',
                secondaryColor: '#5B21B6',
                icon: '<rect x="-18" y="-12" width="36" height="24" fill="white" opacity="0.9" rx="3"/><circle cx="-8" cy="-4" r="2" fill="#7C3AED"/><circle cx="0" cy="-4" r="2" fill="#7C3AED"/><circle cx="8" cy="-4" r="2" fill="#7C3AED"/>',
                label: 'Script Mod'
            };
        }

        if (lowerCategory.includes('texture') || lowerCategory.includes('image')) {
            return {
                primaryColor: '#DC2626',
                secondaryColor: '#B91C1C',
                icon: '<rect x="-18" y="-12" width="36" height="24" fill="white" opacity="0.9" rx="3"/><circle cx="-8" cy="-6" r="3" fill="#DC2626"/><polygon points="-12,8 0,-2 12,8" fill="#DC2626" opacity="0.7"/>',
                label: 'Texture'
            };
        }

        // Default fallback
        return {
            primaryColor: '#6B7280',
            secondaryColor: '#4B5563',
            icon: '<rect x="-16" y="-16" width="32" height="32" fill="white" opacity="0.9" rx="4"/><text x="0" y="4" text-anchor="middle" fill="#6B7280" font-family="system-ui" font-size="16" font-weight="bold">?</text>',
            label: 'Unknown'
        };
    }

    /**
     * Truncates file name for display
     */
    private static truncateFileName(fileName: string): string {
        const nameWithoutExt = fileName.replace(/\.(package|ts4script)$/i, '');
        return nameWithoutExt.length > 20 ? nameWithoutExt.substring(0, 17) + '...' : nameWithoutExt;
    }

    /**
     * Creates a browser-compatible fallback thumbnail when extraction fails
     */
    private static createBrowserFallbackThumbnail(fileName: string, options: Required<ThumbnailExtractionOptions>): ThumbnailData | null {
        try {
            // Determine category from filename
            const category = this.determineCategoryFromFilename(fileName);

            // Generate a simple SVG icon based on category
            const iconData = this.generateSimpleCategoryIcon(category, options.maxWidth, options.maxHeight);

            return {
                id: `${fileName}-fallback-${Date.now()}`,
                modFileName: fileName,
                resourceType: 'Browser Fallback',
                resourceKey: 'fallback',
                imageData: iconData,
                format: 'svg',
                width: options.maxWidth,
                height: options.maxHeight,
                category,
                confidence: 30, // Low confidence for fallback
                extractionMethod: 'browser_fallback',
                fileSize: iconData.length,
                isHighQuality: false,
                isFallback: true
            };
        } catch (error) {
            console.warn('Failed to create browser fallback thumbnail:', error);
            return null;
        }
    }

    /**
     * Determines category from filename patterns
     */
    private static determineCategoryFromFilename(fileName: string): string {
        const lowerName = fileName.toLowerCase();

        if (lowerName.includes('hair') || lowerName.includes('cas')) return 'Create-a-Sim';
        if (lowerName.includes('clothing') || lowerName.includes('outfit')) return 'Clothing';
        if (lowerName.includes('furniture') || lowerName.includes('object')) return 'Objects';
        if (lowerName.includes('build') || lowerName.includes('wall') || lowerName.includes('floor')) return 'Build Mode';
        if (lowerName.includes('trait') || lowerName.includes('aspiration')) return 'Gameplay';

        return 'Unknown';
    }

    /**
     * Generates a simple SVG icon for the category
     */
    private static generateSimpleCategoryIcon(category: string, width: number, height: number): string {
        const colors = {
            'Create-a-Sim': '#FF6B6B',
            'Clothing': '#4ECDC4',
            'Objects': '#45B7D1',
            'Build Mode': '#96CEB4',
            'Gameplay': '#FFEAA7',
            'Unknown': '#DDA0DD'
        };

        const color = colors[category] || colors['Unknown'];
        const initial = category.charAt(0).toUpperCase();

        const svg = `
            <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="${color}" rx="8"/>
                <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${Math.min(width, height) * 0.4}"
                      fill="white" text-anchor="middle" dominant-baseline="central">${initial}</text>
                <text x="50%" y="80%" font-family="Arial, sans-serif" font-size="${Math.min(width, height) * 0.1}"
                      fill="white" text-anchor="middle" opacity="0.8">${category}</text>
            </svg>
        `;

        // Use Unicode-safe base64 encoding
        return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`;
    }

    /**
     * Generates a hash for cache validation
     */
    private static generateFileHash(buffer: Buffer): string {
        // Use a simple hash in browser environment, crypto in Node.js
        if (typeof window !== 'undefined') {
            // Browser environment - simple hash
            const size = buffer.length;
            const first = buffer.length > 0 ? buffer[0] : 0;
            const last = buffer.length > 1 ? buffer[buffer.length - 1] : 0;
            const middle = buffer.length > 2 ? buffer[Math.floor(buffer.length / 2)] : 0;
            return `${size}-${first}-${middle}-${last}`;
        } else {
            // Node.js environment - use crypto
            const crypto = require('crypto');
            return crypto.createHash('md5').update(buffer).digest('hex');
        }
    }
}
